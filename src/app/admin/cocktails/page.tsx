'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { getAdminCocktails, deleteCocktail } from '@/utils/adminDataUtils';
import { useToast } from '@/contexts/ToastContext';
import { Cocktail } from '@/types/cocktail';

export default function AdminCocktailsPage() {
  const { showSuccess, showError } = useToast();
  const [cocktails, setCocktails] = useState<Cocktail[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadCocktails();
  }, []);

  const loadCocktails = async () => {
    setIsLoading(true);
    try {
      const allCocktails = await getAdminCocktails();
      setCocktails(allCocktails);
    } catch (error) {
      console.error('Error loading cocktails:', error);
      setCocktails([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteCocktail = async (cocktailId: string, cocktailName: string) => {
    if (window.confirm(`Are you sure you want to delete "${cocktailName}"? This action cannot be undone.`)) {
      try {
        const success = await deleteCocktail(cocktailId);
        if (success) {
          await loadCocktails(); // Refresh the list
          showSuccess('Cocktail Deleted', `${cocktailName} has been deleted successfully.`);
        } else {
          showError('Delete Failed', 'Error deleting cocktail. Please try again.');
        }
      } catch (error) {
        console.error('Error deleting cocktail:', error);
        showError('Delete Error', 'An error occurred while deleting the cocktail.');
      }
    }
  };

  // Filter cocktails based on search and category
  const filteredCocktails = cocktails.filter(cocktail => {
    const matchesSearch = cocktail.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         cocktail.description.toLowerCase().includes(searchTerm.toLowerCase());
    const categoryValue = typeof cocktail.category === 'string' ? cocktail.category : cocktail.category.id;
    const matchesCategory = !selectedCategory || categoryValue === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const categories = [...new Set(cocktails.map(c =>
    typeof c.category === 'string' ? c.category : c.category.id
  ))];

  if (isLoading) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">⏳</div>
        <div className="text-xl font-semibold text-gray-900">Loading cocktails...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Manage Cocktails</h1>
            <p className="text-gray-600">Add, edit, or remove cocktail recipes</p>
          </div>
          <Link
            href="/admin/cocktails/new"
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
          >
            <span>➕</span>
            Add New Cocktail
          </Link>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg shadow p-4">
            <div className="text-2xl font-bold text-blue-600">{cocktails.length}</div>
            <div className="text-sm text-gray-600">Total Cocktails</div>
          </div>
          <div className="bg-white rounded-lg shadow p-4">
            <div className="text-2xl font-bold text-green-600">{categories.length}</div>
            <div className="text-sm text-gray-600">Categories</div>
          </div>
          <div className="bg-white rounded-lg shadow p-4">
            <div className="text-2xl font-bold text-purple-600">
              {cocktails.filter(c => c.difficulty === 'easy').length}
            </div>
            <div className="text-sm text-gray-600">Easy Recipes</div>
          </div>
          <div className="bg-white rounded-lg shadow p-4">
            <div className="text-2xl font-bold text-orange-600">
              {Math.round(cocktails.reduce((sum, c) => sum + c.prepTime, 0) / cocktails.length) || 0}
            </div>
            <div className="text-sm text-gray-600">Avg. Prep Time</div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search Cocktails
              </label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by name or description..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Filter by Category
              </label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Categories</option>
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category.charAt(0).toUpperCase() + category.slice(1).replace('_', ' ')}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Cocktails Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">
              Cocktails ({filteredCocktails.length})
            </h2>
          </div>
          
          {filteredCocktails.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">🍸</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No cocktails found</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || selectedCategory 
                  ? 'Try adjusting your search or filter criteria.'
                  : 'Get started by adding your first cocktail.'}
              </p>
              <Link
                href="/admin/cocktails/new"
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Add New Cocktail
              </Link>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Category
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Difficulty
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Prep Time
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ingredients
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredCocktails.map((cocktail) => (
                    <tr key={cocktail.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{cocktail.name}</div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {cocktail.description}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                          {typeof cocktail.category === 'string'
                            ? cocktail.category.replace('_', ' ')
                            : cocktail.category.name
                          }
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          cocktail.difficulty === 'easy' ? 'bg-green-100 text-green-800' :
                          cocktail.difficulty === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {cocktail.difficulty}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {cocktail.prepTime} min
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {cocktail.ingredients.length} items
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <Link
                          href={`/cocktail/${cocktail.id}`}
                          className="text-blue-600 hover:text-blue-900"
                          target="_blank"
                        >
                          View
                        </Link>
                        <Link
                          href={`/admin/cocktails/edit/${cocktail.id}`}
                          className="text-indigo-600 hover:text-indigo-900"
                        >
                          Edit
                        </Link>
                        <button
                          onClick={() => handleDeleteCocktail(cocktail.id, cocktail.name)}
                          className="text-red-600 hover:text-red-900"
                        >
                          Delete
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
  );
}
