{"name": "tipple", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@supabase/supabase-js": "^2.50.3", "next": "15.3.5", "next-pwa": "^5.6.0", "react": "^19.0.0", "react-dom": "^19.0.0", "workbox-webpack-plugin": "^7.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^17.0.1", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}